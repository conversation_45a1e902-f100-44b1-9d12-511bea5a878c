# PulseOps
Network telemetry and task runner with web UI.

## Docker
```bash
docker compose up --build -d
# visit http://localhost:8765
```

## Native
```bash
cd cmd/pulseops && go build -o pulseops && ./pulseops -config ../../config.sample.yml
```

## Snap
```bash
snapcraft
sudo snap install pulseops_0.2_*.snap --dangerous --classic
pulseops
```

## API
- GET  /api/devices
- POST /api/devices/import *(import devices from exported JSON)*
- GET  /api/export/devices *(download all devices as JSON)*
- GET  /api/device-backups?device_id=ID *(list captured backups for a device)*
- POST /api/device-backups *(trigger a new device backup)*
- GET  /api/device-backups/ID *(download a specific backup)*
- GET  /api/metrics/latest?device_id=ID&metric=ping_ms
- GET  /api/metrics?device_id=ID&metric=ping_ms&since=RFC3339&limit=500
- GET  /api/tasks?device_id=ID
- POST /api/tasks {"device_id":ID,"kind":"reboot|refresh_firewall|refresh_wireless","args":"","by":"web"}

## Device import & backups

PulseOps can import devices from the same JSON document it produces via **Export → Devices** in the web UI.
Use the **Import** action in the Devices table or POST to `/api/devices/import` with a JSON array of device objects (or a `{ "devices": [...] }` wrapper).

For device platforms that support programmatic configuration backups (for example OpenWRT and EdgeOS), PulseOps can trigger, store, and download backup archives.
Backups are available from the device overview screen in the web UI or through the `/api/device-backups` endpoints listed above.
The latest backup timestamp and download link are displayed on the overview page, and older backups can be downloaded from the backup history modal.

## Huawei
Set `platform: huawei` with `user` and `password`. The driver logs in via web API, then attempts reboot across several known endpoints.
