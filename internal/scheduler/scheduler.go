package scheduler

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/pulseops/pulseops/internal/backups"
	"github.com/pulseops/pulseops/internal/collectors/iperf3c"
	"github.com/pulseops/pulseops/internal/collectors/ping"
	"github.com/pulseops/pulseops/internal/config"
	"github.com/pulseops/pulseops/internal/executil"
	"github.com/pulseops/pulseops/internal/keys"
	"github.com/pulseops/pulseops/internal/store"
)

const (
	deviceSyncInterval = 10 * time.Second
	pingInterval       = 30 * time.Second
	pingTimeout        = 5 * time.Second
	pingBudget         = 8 * time.Second
)

type deviceRunner struct {
	cancel context.CancelFunc
	state  deviceState
}

type deviceState struct {
	record         store.DeviceRecord
	meta           map[string]string
	sshPort        int
	iperfEnabled   bool
	iperfInterval  time.Duration
	iperfDuration  int
	iperfParallel  int
	backupEnabled  bool
	backupInterval time.Duration
}

type Svc struct {
	Cfg     *config.Config
	DB      *store.Store
	Keys    *keys.Manager
	Backups *backups.Manager
	Stop    chan struct{}

	wg        sync.WaitGroup
	devicesMu sync.Mutex
	devices   map[int64]*deviceRunner
}

func New(cfg *config.Config, db *store.Store, keyManager *keys.Manager, backupManager *backups.Manager) *Svc {
	return &Svc{
		Cfg:     cfg,
		DB:      db,
		Keys:    keyManager,
		Backups: backupManager,
		Stop:    make(chan struct{}),
		devices: make(map[int64]*deviceRunner),
	}
}

func (s *Svc) Start() {
	s.bootstrapConfigDevices()

	s.wg.Add(1)
	go s.deviceSyncLoop()
}

func (s *Svc) bootstrapConfigDevices() {
	for _, d := range s.Cfg.Devices {
		deleted, err := s.DB.IsDeviceDeleted(d.Name)
		if err != nil {
			log.Printf("device skip check %s: %v", d.Name, err)
			continue
		}
		if deleted {
			log.Printf("device %s skipped (marked deleted)", d.Name)
			continue
		}
		if _, err := s.DB.UpsertDevice(d.Name, d.Host, d.Kind, d.Platform, d.User, d.SSHKey, d.Password, ""); err != nil {
			log.Printf("device upsert %s: %v", d.Name, err)
		}
	}
}

func (s *Svc) deviceSyncLoop() {
	defer s.wg.Done()
	defer s.stopAllDevices()

	if err := s.syncDevices(); err != nil {
		log.Printf("scheduler sync: %v", err)
	}

	ticker := time.NewTicker(deviceSyncInterval)
	defer ticker.Stop()

	for {
		select {
		case <-s.Stop:
			return
		case <-ticker.C:
			if err := s.syncDevices(); err != nil {
				log.Printf("scheduler sync: %v", err)
			}
		}
	}
}

func (s *Svc) syncDevices() error {
	devices, err := s.DB.ListDeviceRecords()
	if err != nil {
		return err
	}

	next := make(map[int64]store.DeviceRecord, len(devices))
	for _, dev := range devices {
		next[dev.ID] = dev
	}

	s.devicesMu.Lock()
	defer s.devicesMu.Unlock()

	for id, runner := range s.devices {
		dev, ok := next[id]
		if !ok || deviceChanged(runner.state.record, dev) {
			runner.cancel()
			delete(s.devices, id)
		}
	}

	for id, dev := range next {
		if _, ok := s.devices[id]; ok {
			continue
		}
		s.startDeviceLocked(dev)
	}

	return nil
}

func deviceChanged(a, b store.DeviceRecord) bool {
	return a.Name != b.Name || a.Host != b.Host || a.Kind != b.Kind || a.Platform != b.Platform || a.User != b.User || a.SSHKey != b.SSHKey || a.Password != b.Password || a.Meta != b.Meta
}

func (s *Svc) startDeviceLocked(dev store.DeviceRecord) {
	ctx, cancel := context.WithCancel(context.Background())
	state := s.buildDeviceState(dev)
	s.devices[dev.ID] = &deviceRunner{cancel: cancel, state: state}

	s.wg.Add(1)
	go func(st deviceState) {
		defer s.wg.Done()
		s.runDeviceLoop(ctx, st)
	}(state)
}

func (s *Svc) runDeviceLoop(ctx context.Context, state deviceState) {
	pingTicker := time.NewTicker(pingInterval)
	defer pingTicker.Stop()

	var iperfTicker *time.Ticker
	var iperfC <-chan time.Time
	if state.iperfEnabled && state.iperfInterval > 0 {
		iperfTicker = time.NewTicker(state.iperfInterval)
		iperfC = iperfTicker.C
		defer iperfTicker.Stop()
		s.collectIperf(ctx, state)
	}

	var (
		backupTimer *time.Timer
		backupC     <-chan time.Time
	)
	scheduleBackup := func() {}
	if state.backupEnabled && s.Backups != nil {
		scheduleBackup = func() {
			if backupTimer != nil {
				backupTimer.Stop()
				backupTimer = nil
				backupC = nil
			}
			due, next, _ := s.backupSchedule(state)
			if !due && next.IsZero() {
				return
			}
			delay := time.Minute
			if !due {
				delay = time.Until(next)
				if delay < time.Minute {
					delay = time.Minute
				}
			}
			backupTimer = time.NewTimer(delay)
			backupC = backupTimer.C
		}
		scheduleBackup()
	}
	defer func() {
		if backupTimer != nil {
			backupTimer.Stop()
		}
	}()

	s.collectPing(ctx, state.record)

	for {
		select {
		case <-ctx.Done():
			return
		case <-pingTicker.C:
			s.collectPing(ctx, state.record)
		case <-iperfC:
			s.collectIperf(ctx, state)
		case <-backupC:
			if s.Backups == nil || !state.backupEnabled {
				continue
			}
			due, _, _ := s.backupSchedule(state)
			if !due {
				scheduleBackup()
				continue
			}
			s.captureScheduledBackup(ctx, state)
			scheduleBackup()
		}
	}
}

func (s *Svc) collectPing(parent context.Context, dev store.DeviceRecord) {
	if dev.Host == "" {
		return
	}

	ctx, cancel := context.WithTimeout(parent, pingBudget)
	defer cancel()

	avg, err := ping.PingOnce(ctx, dev.Host, pingTimeout)
	if err != nil {
		if ctx.Err() == nil {
			log.Printf("ping %s: %v", dev.Name, err)
			s.recordDeviceLogf(dev.ID, "error", "Ping failed: %v", err)
		}
		return
	}

	metric := store.Metric{
		DeviceID: dev.ID,
		TS:       time.Now(),
		Metric:   "ping_ms",
		Value:    sql.NullFloat64{Float64: avg, Valid: true},
		Unit:     sql.NullString{String: "ms", Valid: true},
	}
	if err := s.DB.InsertMetric(metric); err != nil {
		log.Printf("store ping metric %d: %v", dev.ID, err)
		return
	}

	s.recordDeviceLogf(dev.ID, "info", "Ping %.1f ms", avg)
}

func (s *Svc) collectIperf(parent context.Context, state deviceState) {
	if !state.iperfEnabled || state.record.Host == "" {
		return
	}
	if state.record.User == "" {
		log.Printf("iperf %s skipped: missing SSH user", state.record.Name)
		s.recordDeviceLogf(state.record.ID, "warn", "iPerf skipped: missing SSH user")
		return
	}

	keyPath, cleanup, err := s.resolveKeyPath(state.record.SSHKey)
	if err != nil {
		log.Printf("iperf %s: %v", state.record.Name, err)
		s.recordDeviceLogf(state.record.ID, "error", "iPerf key resolution failed: %v", err)
		return
	}
	defer cleanup()

	timeout := s.iperfTimeout(state)
	ctx, cancel := context.WithTimeout(parent, timeout)
	defer cancel()

	if err := s.ensureIperfAvailable(ctx, state, keyPath); err != nil {
		if ctx.Err() == nil {
			log.Printf("iperf %s: %v", state.record.Name, err)
			s.recordDeviceLogf(state.record.ID, "error", "iPerf preparation failed: %v", err)
		}
		return
	}

	pid, err := s.ensureIperfServer(ctx, state, keyPath)
	if err != nil {
		if ctx.Err() == nil {
			log.Printf("iperf %s: %v", state.record.Name, err)
			s.recordDeviceLogf(state.record.ID, "error", "iPerf server setup failed: %v", err)
		}
		return
	}

	startedByUs := pid != ""
	if startedByUs {
		select {
		case <-time.After(2 * time.Second):
		case <-ctx.Done():
			return
		}
	}

	mbps, raw, err := iperf3c.Run(ctx, state.record.Host, state.iperfDuration, state.iperfParallel)
	if err != nil {
		if ctx.Err() == nil {
			log.Printf("iperf %s: %v", state.record.Name, err)
			s.recordDeviceLogf(state.record.ID, "error", "iPerf run failed: %v", err)
		}
		if startedByUs && pid != "" {
			s.stopIperfServer(context.Background(), state, keyPath, pid)
		}
		return
	}

	metric := store.Metric{
		DeviceID: state.record.ID,
		TS:       time.Now(),
		Metric:   "iperf_mbps",
		Value:    sql.NullFloat64{Float64: mbps, Valid: true},
		Unit:     sql.NullString{String: "Mbps", Valid: true},
		Raw:      sql.NullString{String: raw, Valid: true},
	}
	if err := s.DB.InsertMetric(metric); err != nil {
		log.Printf("store iperf metric %d: %v", state.record.ID, err)
		return
	}

	if startedByUs && pid != "" {
		s.stopIperfServer(context.Background(), state, keyPath, pid)
	}

	s.recordDeviceLogf(state.record.ID, "info", "iPerf %.1f Mbps", mbps)
}

func (s *Svc) ensureIperfAvailable(ctx context.Context, state deviceState, keyPath string) error {
	check := s.runSSH(ctx, 30*time.Second, keyPath, state.sshPort, state.record.User, state.record.Host, "sh -c 'command -v iperf3 || which iperf3'")
	if check.Err == nil {
		return nil
	}

	switch strings.ToLower(state.record.Platform) {
	case "openwrt":
		install := s.runSSH(ctx, 4*time.Minute, keyPath, state.sshPort, state.record.User, state.record.Host, "opkg update && opkg install iperf3")
		if install.Err != nil {
			return fmt.Errorf("install iperf3 via opkg: %v; %s", install.Err, trimOutput(install.Stdout+install.Stderr))
		}
		verify := s.runSSH(ctx, 30*time.Second, keyPath, state.sshPort, state.record.User, state.record.Host, "sh -c 'command -v iperf3 || which iperf3'")
		if verify.Err != nil {
			return fmt.Errorf("iperf3 still unavailable: %s", trimOutput(verify.Stdout+verify.Stderr))
		}
		return nil
	default:
		return fmt.Errorf("iperf3 not installed on platform %s", state.record.Platform)
	}
}

func (s *Svc) ensureIperfServer(ctx context.Context, state deviceState, keyPath string) (string, error) {
	probe := s.runSSH(ctx, 15*time.Second, keyPath, state.sshPort, state.record.User, state.record.Host, "sh -c 'pgrep iperf3 || pidof iperf3'")
	if probe.Err == nil && strings.TrimSpace(probe.Stdout) != "" {
		return "", nil
	}

	start := s.runSSH(ctx, 15*time.Second, keyPath, state.sshPort, state.record.User, state.record.Host, "sh -c 'iperf3 -s -1 >/tmp/pulseops_iperf.log 2>&1 & echo $!'")
	if start.Err != nil {
		return "", fmt.Errorf("start iperf3 server: %v; %s", start.Err, trimOutput(start.Stdout+start.Stderr))
	}
	pid := strings.TrimSpace(start.Stdout)
	if pid == "" {
		return "", fmt.Errorf("iperf3 server pid not returned")
	}
	return pid, nil
}

func (s *Svc) stopIperfServer(parent context.Context, state deviceState, keyPath, pid string) {
	if pid == "" {
		return
	}
	ctx, cancel := context.WithTimeout(parent, 10*time.Second)
	defer cancel()
	cmd := fmt.Sprintf("sh -c 'if kill -0 %s 2>/dev/null; then kill %s; fi'", pid, pid)
	s.runSSH(ctx, 10*time.Second, keyPath, state.sshPort, state.record.User, state.record.Host, cmd)
}

func (s *Svc) runSSH(ctx context.Context, timeout time.Duration, keyPath string, port int, user, host, command string) executil.Result {
	if user == "" || host == "" {
		return executil.Result{Err: fmt.Errorf("missing ssh parameters")}
	}

	args := []string{
		"-o", "BatchMode=yes",
		"-o", "StrictHostKeyChecking=no",
		"-o", "UserKnownHostsFile=/dev/null",
		"-o", "GlobalKnownHostsFile=/dev/null",
		"-o", "ConnectTimeout=10",
	}
	if keyPath != "" {
		args = append(args, "-i", keyPath)
	}
	if port > 0 && port != 22 {
		args = append(args, "-p", strconv.Itoa(port))
	}
	target := fmt.Sprintf("%s@%s", user, host)
	args = append(args, target, command)
	return executil.Run(ctx, timeout, "ssh", args...)
}

func (s *Svc) stopAllDevices() {
	s.devicesMu.Lock()
	defer s.devicesMu.Unlock()
	for id, runner := range s.devices {
		runner.cancel()
		delete(s.devices, id)
	}
}

func (s *Svc) Shutdown() {
	close(s.Stop)
	s.wg.Wait()
}

func (s *Svc) recordDeviceLogf(deviceID int64, level string, format string, args ...any) {
	msg := fmt.Sprintf(format, args...)
	if strings.TrimSpace(msg) == "" {
		return
	}
	if err := s.DB.InsertDeviceLog(deviceID, level, msg); err != nil {
		log.Printf("device log insert %d: %v", deviceID, err)
	}
}

func (s *Svc) backupSchedule(state deviceState) (bool, time.Time, time.Duration) {
	if s.Backups == nil || !state.backupEnabled {
		return false, time.Time{}, 0
	}
	interval := state.backupInterval
	if interval <= 0 {
		interval = backups.DefaultInterval
	}
	now := time.Now().UTC()
	latest, err := s.DB.LatestDeviceBackup(state.record.ID)
	if err == nil {
		if now.Sub(latest.CreatedAt) >= interval {
			return true, now, interval
		}
		next := backups.NextScheduled(latest.CreatedAt, now, interval)
		return false, next, interval
	}
	if errors.Is(err, sql.ErrNoRows) {
		return true, now, interval
	}
	log.Printf("scheduled backup lookup %s: %v", state.record.Name, err)
	return true, now.Add(5 * time.Minute), interval
}

func (s *Svc) captureScheduledBackup(ctx context.Context, state deviceState) {
	if s.Backups == nil {
		return
	}
	if _, err := s.Backups.Capture(ctx, state.record.ID); err != nil {
		if ctx.Err() != nil {
			return
		}
		log.Printf("scheduled backup %s: %v", state.record.Name, err)
		s.recordDeviceLogf(state.record.ID, "error", "Scheduled backup failed: %v", err)
		if s.DB != nil {
			_ = s.DB.InsertSystemLog("error", "device.backup", fmt.Sprintf("Scheduled backup failed for %s", state.record.Name), map[string]any{
				"device_id": state.record.ID,
				"platform":  state.record.Platform,
				"mode":      "scheduled",
			})
		}
	}
}

func (s *Svc) buildDeviceState(dev store.DeviceRecord) deviceState {
	meta := parseMeta(dev.Meta)
	state := deviceState{
		record:        dev,
		meta:          meta,
		sshPort:       parseSSHPort(meta),
		iperfEnabled:  true,
		iperfInterval: s.defaultIperfInterval(),
		iperfDuration: s.defaultIperfDuration(),
		iperfParallel: s.defaultIperfParallel(),
	}

	if val, ok := meta["iperf_enabled"]; ok {
		if enabled, parsed := parseBool(val); parsed {
			state.iperfEnabled = enabled
		}
	}
	if val, ok := meta["iperf_interval"]; ok {
		if d, ok := parseDurationOrMinutes(val); ok && d > 0 {
			state.iperfInterval = d
		}
	}
	if val, ok := meta["iperf_seconds"]; ok {
		if secs := toPositiveInt(val); secs > 0 {
			state.iperfDuration = secs
		}
	} else if val, ok := meta["iperf_duration"]; ok {
		if secs := toPositiveInt(val); secs > 0 {
			state.iperfDuration = secs
		}
	}
	if val, ok := meta["iperf_parallel"]; ok {
		if par := toPositiveInt(val); par > 0 {
			state.iperfParallel = par
		}
	}

	if state.iperfInterval <= 0 {
		state.iperfEnabled = false
	}

	if s.Backups != nil && backups.Supports(dev.Platform) {
		state.backupEnabled = true
		state.backupInterval = backups.IntervalFromMeta(dev.Meta)
	}

	return state
}

func (s *Svc) defaultIperfInterval() time.Duration {
	mins := s.Cfg.Iperf.IntervalMinutes
	if mins <= 0 {
		return time.Hour
	}
	return time.Duration(mins) * time.Minute
}

func (s *Svc) defaultIperfDuration() int {
	secs := s.Cfg.Iperf.Seconds
	if secs <= 0 {
		return 10
	}
	return secs
}

func (s *Svc) defaultIperfParallel() int {
	par := s.Cfg.Iperf.Parallel
	if par <= 0 {
		return 1
	}
	return par
}

func (s *Svc) iperfTimeout(state deviceState) time.Duration {
	base := time.Duration(state.iperfDuration)*time.Second + 2*time.Minute
	if base < 3*time.Minute {
		base = 3 * time.Minute
	}
	return base
}

func (s *Svc) resolveKeyPath(value string) (string, func(), error) {
	if value == "" {
		return "", func() {}, nil
	}
	if s.Keys == nil {
		return value, func() {}, nil
	}
	return s.Keys.ResolvePath(value)
}

func parseMeta(raw string) map[string]string {
	out := map[string]string{}
	raw = strings.TrimSpace(raw)
	if raw == "" {
		return out
	}
	var decoded map[string]any
	if err := json.Unmarshal([]byte(raw), &decoded); err != nil {
		return out
	}
	for k, v := range decoded {
		switch t := v.(type) {
		case string:
			out[k] = t
		case float64:
			out[k] = strconv.FormatFloat(t, 'f', -1, 64)
		case bool:
			if t {
				out[k] = "true"
			} else {
				out[k] = "false"
			}
		default:
			b, err := json.Marshal(t)
			if err == nil {
				out[k] = string(b)
			}
		}
	}
	return out
}

func parseSSHPort(meta map[string]string) int {
	if val, ok := meta["ssh_port"]; ok {
		if port := toPositiveInt(val); port > 0 && port <= 65535 {
			return port
		}
	}
	return 22
}

func parseBool(value string) (bool, bool) {
	switch strings.ToLower(strings.TrimSpace(value)) {
	case "1", "true", "yes", "on":
		return true, true
	case "0", "false", "no", "off":
		return false, true
	default:
		return false, false
	}
}

func parseDurationOrMinutes(value string) (time.Duration, bool) {
	value = strings.TrimSpace(value)
	if value == "" {
		return 0, false
	}
	if d, err := time.ParseDuration(value); err == nil {
		return d, true
	}
	if mins, err := strconv.Atoi(value); err == nil {
		return time.Duration(mins) * time.Minute, true
	}
	return 0, false
}

func toPositiveInt(value string) int {
	value = strings.TrimSpace(value)
	if value == "" {
		return 0
	}
	n, err := strconv.Atoi(value)
	if err != nil || n <= 0 {
		return 0
	}
	return n
}

func trimOutput(out string) string {
	out = strings.TrimSpace(out)
	if len(out) > 240 {
		return out[:240] + "..."
	}
	return out
}
