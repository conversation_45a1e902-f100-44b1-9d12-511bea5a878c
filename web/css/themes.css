/* PulseOps Theme System */

:root {
  /* Light Theme (Default) */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --bg-accent: #f0f8ff;
  
  --text-primary: #1a1a1a;
  --text-secondary: #666666;
  --text-muted: #888888;
  --text-inverse: #ffffff;
  
  --border-primary: #e5e5e5;
  --border-secondary: #ddd;
  --border-accent: #007bff;
  
  --accent-primary: #007bff;
  --accent-primary-hover: #0056b3;
  --accent-secondary: #6c757d;
  --accent-success: #28a745;
  --accent-warning: #ffc107;
  --accent-danger: #dc3545;
  
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.08);
  --shadow-md: 0 4px 12px rgba(0, 123, 255, 0.15);
  --shadow-lg: 0 8px 24px rgba(0, 123, 255, 0.2);
  
  --gradient-primary: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
  --gradient-secondary: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* Dark Theme */
[data-theme="dark"] {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #404040;
  --bg-accent: #1e3a5f;
  
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-muted: #999999;
  --text-inverse: #1a1a1a;
  
  --border-primary: #404040;
  --border-secondary: #555555;
  --border-accent: #4a9eff;
  
  --accent-primary: #4a9eff;
  --accent-primary-hover: #357abd;
  --accent-secondary: #6c757d;
  --accent-success: #40c057;
  --accent-warning: #fab005;
  --accent-danger: #fa5252;
  
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 12px rgba(74, 158, 255, 0.2);
  --shadow-lg: 0 8px 24px rgba(74, 158, 255, 0.3);
  
  --gradient-primary: linear-gradient(135deg, #1e3a5f 0%, #2d4a6b 100%);
  --gradient-secondary: linear-gradient(135deg, #2d2d2d 0%, #404040 100%);
}

/* Retro Terminal Theme */
[data-theme="retro"] {
  --bg-primary: #000000;
  --bg-secondary: #0a0a0a;
  --bg-tertiary: #1a1a1a;
  --bg-accent: #003300;
  
  --text-primary: #00ff00;
  --text-secondary: #00cc00;
  --text-muted: #008800;
  --text-inverse: #000000;
  
  --border-primary: #00ff00;
  --border-secondary: #00cc00;
  --border-accent: #00ff00;
  
  --accent-primary: #00ff00;
  --accent-primary-hover: #00cc00;
  --accent-secondary: #ffff00;
  --accent-success: #00ff00;
  --accent-warning: #ffff00;
  --accent-danger: #ff0000;
  
  --shadow-sm: 0 0 8px rgba(0, 255, 0, 0.3);
  --shadow-md: 0 0 12px rgba(0, 255, 0, 0.5);
  --shadow-lg: 0 0 24px rgba(0, 255, 0, 0.7);
  
  --gradient-primary: linear-gradient(135deg, #003300 0%, #001100 100%);
  --gradient-secondary: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  
  /* Terminal-specific styles */
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  text-shadow: 0 0 5px currentColor;
}

/* Sophisticated Blue Theme */
[data-theme="sophisticated"] {
  --bg-primary: #f7f9fc;
  --bg-secondary: #eef4f9;
  --bg-tertiary: #e1ecf4;
  --bg-accent: #e8f4fd;
  
  --text-primary: #1e3a8a;
  --text-secondary: #3b82f6;
  --text-muted: #6b7280;
  --text-inverse: #ffffff;
  
  --border-primary: #d1d5db;
  --border-secondary: #9ca3af;
  --border-accent: #3b82f6;
  
  --accent-primary: #3b82f6;
  --accent-primary-hover: #2563eb;
  --accent-secondary: #6b7280;
  --accent-success: #10b981;
  --accent-warning: #f59e0b;
  --accent-danger: #ef4444;
  
  --shadow-sm: 0 2px 8px rgba(59, 130, 246, 0.08);
  --shadow-md: 0 4px 12px rgba(59, 130, 246, 0.15);
  --shadow-lg: 0 8px 24px rgba(59, 130, 246, 0.2);
  
  --gradient-primary: linear-gradient(135deg, #e8f4fd 0%, #dbeafe 100%);
  --gradient-secondary: linear-gradient(135deg, #eef4f9 0%, #e1ecf4 100%);
}

/* Theme-specific animations and effects */
[data-theme="retro"] * {
  animation: terminal-flicker 0.15s infinite linear alternate;
}

[data-theme="retro"] .template-card,
[data-theme="retro"] .device-card {
  border-style: solid;
  border-width: 1px;
  background: var(--bg-primary);
}

[data-theme="retro"] .template-card:hover,
[data-theme="retro"] .device-card:hover {
  box-shadow: 0 0 10px var(--accent-primary);
}

@keyframes terminal-flicker {
  0% { opacity: 1; }
  100% { opacity: 0.98; }
}

/* System theme detection */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #404040;
    --bg-accent: #1e3a5f;
    
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #999999;
    --text-inverse: #1a1a1a;
    
    --border-primary: #404040;
    --border-secondary: #555555;
    --border-accent: #4a9eff;
    
    --accent-primary: #4a9eff;
    --accent-primary-hover: #357abd;
    --accent-secondary: #6c757d;
    --accent-success: #40c057;
    --accent-warning: #fab005;
    --accent-danger: #fa5252;
    
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 12px rgba(74, 158, 255, 0.2);
    --shadow-lg: 0 8px 24px rgba(74, 158, 255, 0.3);
    
    --gradient-primary: linear-gradient(135deg, #1e3a5f 0%, #2d4a6b 100%);
    --gradient-secondary: linear-gradient(135deg, #2d2d2d 0%, #404040 100%);
  }
}

/* Smooth theme transitions */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}
