/**
 * PulseOps Theme Manager
 * Handles theme switching and persistence across the application
 */

class ThemeManager {
  constructor() {
    this.themes = {
      'light': 'Light',
      'dark': 'Dark', 
      'retro': 'Retro Terminal',
      'sophisticated': 'Sophisticated Blue',
      'system': 'Match System'
    };
    
    this.currentTheme = this.getStoredTheme() || 'system';
    this.init();
  }
  
  init() {
    this.applyTheme(this.currentTheme);
    this.setupSystemThemeListener();
  }
  
  getStoredTheme() {
    try {
      return localStorage.getItem('pulseops-theme');
    } catch (e) {
      return null;
    }
  }
  
  setStoredTheme(theme) {
    try {
      localStorage.setItem('pulseops-theme', theme);
    } catch (e) {
      console.warn('Failed to store theme preference');
    }
  }
  
  getSystemTheme() {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'dark';
    }
    return 'light';
  }
  
  applyTheme(theme) {
    const root = document.documentElement;
    
    // Remove existing theme attributes
    root.removeAttribute('data-theme');
    
    if (theme === 'system') {
      // Let CSS handle system theme detection
      this.currentTheme = 'system';
    } else {
      root.setAttribute('data-theme', theme);
      this.currentTheme = theme;
    }
    
    // Store the preference
    this.setStoredTheme(theme);
    
    // Dispatch theme change event
    window.dispatchEvent(new CustomEvent('themechange', {
      detail: { theme: theme, effectiveTheme: this.getEffectiveTheme() }
    }));
  }
  
  getEffectiveTheme() {
    if (this.currentTheme === 'system') {
      return this.getSystemTheme();
    }
    return this.currentTheme;
  }
  
  setTheme(theme) {
    if (this.themes.hasOwnProperty(theme)) {
      this.applyTheme(theme);
    }
  }
  
  getTheme() {
    return this.currentTheme;
  }
  
  getAvailableThemes() {
    return { ...this.themes };
  }
  
  setupSystemThemeListener() {
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addListener(() => {
        if (this.currentTheme === 'system') {
          // Re-apply system theme to trigger change event
          this.applyTheme('system');
        }
      });
    }
  }
  
  // Utility method to get theme-aware colors
  getThemeColors() {
    const root = document.documentElement;
    const computedStyle = getComputedStyle(root);
    
    return {
      bgPrimary: computedStyle.getPropertyValue('--bg-primary').trim(),
      bgSecondary: computedStyle.getPropertyValue('--bg-secondary').trim(),
      textPrimary: computedStyle.getPropertyValue('--text-primary').trim(),
      textSecondary: computedStyle.getPropertyValue('--text-secondary').trim(),
      accentPrimary: computedStyle.getPropertyValue('--accent-primary').trim(),
      borderPrimary: computedStyle.getPropertyValue('--border-primary').trim()
    };
  }
  
  // Method to check if current theme is dark
  isDarkTheme() {
    const effectiveTheme = this.getEffectiveTheme();
    return effectiveTheme === 'dark' || effectiveTheme === 'retro';
  }
  
  // Method to get contrast color for text
  getContrastColor(backgroundColor) {
    // Simple contrast calculation
    const rgb = backgroundColor.match(/\d+/g);
    if (!rgb) return this.isDarkTheme() ? '#ffffff' : '#000000';
    
    const brightness = (parseInt(rgb[0]) * 299 + parseInt(rgb[1]) * 587 + parseInt(rgb[2]) * 114) / 1000;
    return brightness > 128 ? '#000000' : '#ffffff';
  }
}

// Create global theme manager instance
window.themeManager = new ThemeManager();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ThemeManager;
}
