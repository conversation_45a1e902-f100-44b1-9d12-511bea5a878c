<!doctype html>
<html>
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>PulseOps Login</title>
  <link rel="stylesheet" href="/css/auth.css" />
</head>
<body class="auth-page">
  <div class="login-container auth-card">
    <div class="login-logo">PulseOps</div>
    <div class="login-subtitle">Sign in to your account</div>

    <div id="error-message" class="error-message"></div>
    <div id="success-message" class="success-message"></div>

    <form id="login-form" class="login-form">
      <div class="form-group">
        <label for="username">Username</label>
        <input type="text" id="username" name="username" required autocomplete="username" />
      </div>
      
      <div class="form-group">
        <label for="password">Password</label>
        <input type="password" id="password" name="password" required autocomplete="current-password" />
      </div>
      
      <button type="submit" class="login-button" id="login-button">
        Sign In
      </button>
    </form>

    <div id="loading" class="loading">
      <div class="spinner"></div>
      <span>Signing you in...</span>
    </div>
  </div>

  <script>
    const loginForm = document.getElementById('login-form');
    const loginButton = document.getElementById('login-button');
    const loading = document.getElementById('loading');
    const errorMessage = document.getElementById('error-message');
    const successMessage = document.getElementById('success-message');

    function showError(message) {
      errorMessage.textContent = message;
      errorMessage.style.display = 'block';
      successMessage.style.display = 'none';
    }

    function showSuccess(message) {
      successMessage.textContent = message;
      successMessage.style.display = 'block';
      errorMessage.style.display = 'none';
    }

    function hideMessages() {
      errorMessage.style.display = 'none';
      successMessage.style.display = 'none';
    }

    function setLoading(isLoading) {
      if (isLoading) {
        loginForm.style.display = 'none';
        loading.style.display = 'flex';
      } else {
        loginForm.style.display = 'flex';
        loading.style.display = 'none';
      }
    }

    // Check authentication status
    async function checkAuthStatus() {
      try {
        const response = await fetch('/api/auth/status');
        const data = await response.json();
        
        if (!data.setup_completed) {
          window.location.href = '/setup.html';
          return;
        }
        
        if (data.authenticated) {
          window.location.href = '/';
          return;
        }
      } catch (error) {
        console.error('Failed to check auth status:', error);
      }
    }

    loginForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      hideMessages();

      const formData = new FormData(loginForm);
      const username = formData.get('username').trim();
      const password = formData.get('password');

      // Validation
      if (!username) {
        showError('Username is required');
        return;
      }

      if (!password) {
        showError('Password is required');
        return;
      }

      setLoading(true);

      try {
        const response = await fetch('/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            username,
            password,
          }),
        });

        if (response.ok) {
          const data = await response.json();
          showSuccess('Login successful! Redirecting...');
          setTimeout(() => {
            window.location.href = '/';
          }, 1000);
        } else {
          const errorText = await response.text();
          showError(errorText || 'Invalid credentials');
        }
      } catch (error) {
        showError('Network error. Please try again.');
      } finally {
        setLoading(false);
      }
    });

    // Check auth status on page load
    checkAuthStatus();
  </script>
</body>
</html>
