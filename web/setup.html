<!doctype html>
<html>
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>PulseOps Setup</title>
  <link rel="stylesheet" href="/css/auth.css" />
</head>
<body class="auth-page">
  <div class="setup-container auth-card">
    <div class="setup-logo">PulseOps</div>
    <div class="setup-subtitle">Welcome! Let's set up your admin account.</div>
    
    <div class="setup-info">
      <h3>🔐 Admin Account Setup</h3>
      <p>Create your administrator account to get started with PulseOps. All users on this instance will have admin capabilities.</p>
    </div>

    <div id="error-message" class="error-message"></div>
    <div id="success-message" class="success-message"></div>

    <form id="setup-form" class="setup-form">
      <div class="form-group">
        <label for="username">Username</label>
        <input type="text" id="username" name="username" required autocomplete="username" />
      </div>
      
      <div class="form-group">
        <label for="email">Email (optional)</label>
        <input type="email" id="email" name="email" autocomplete="email" />
      </div>
      
      <div class="form-group">
        <label for="password">Password</label>
        <input type="password" id="password" name="password" required autocomplete="new-password" minlength="6" />
      </div>
      
      <div class="form-group">
        <label for="confirm-password">Confirm Password</label>
        <input type="password" id="confirm-password" name="confirm-password" required autocomplete="new-password" minlength="6" />
      </div>
      
      <button type="submit" class="setup-button" id="setup-button">
        Create Admin Account
      </button>
    </form>

    <div id="loading" class="loading">
      <div class="spinner"></div>
      <span>Setting up your account...</span>
    </div>
  </div>

  <script>
    const setupForm = document.getElementById('setup-form');
    const setupButton = document.getElementById('setup-button');
    const loading = document.getElementById('loading');
    const errorMessage = document.getElementById('error-message');
    const successMessage = document.getElementById('success-message');

    function showError(message) {
      errorMessage.textContent = message;
      errorMessage.style.display = 'block';
      successMessage.style.display = 'none';
    }

    function showSuccess(message) {
      successMessage.textContent = message;
      successMessage.style.display = 'block';
      errorMessage.style.display = 'none';
    }

    function hideMessages() {
      errorMessage.style.display = 'none';
      successMessage.style.display = 'none';
    }

    function setLoading(isLoading) {
      if (isLoading) {
        setupForm.style.display = 'none';
        loading.style.display = 'flex';
      } else {
        setupForm.style.display = 'flex';
        loading.style.display = 'none';
      }
    }

    // Check if setup is already completed
    async function checkSetupStatus() {
      try {
        const response = await fetch('/api/auth/setup');
        const data = await response.json();
        if (data.setup_completed) {
          window.location.href = '/';
        }
      } catch (error) {
        console.error('Failed to check setup status:', error);
      }
    }

    setupForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      hideMessages();

      const formData = new FormData(setupForm);
      const username = formData.get('username').trim();
      const email = formData.get('email').trim();
      const password = formData.get('password');
      const confirmPassword = formData.get('confirm-password');

      // Validation
      if (!username) {
        showError('Username is required');
        return;
      }

      if (password.length < 6) {
        showError('Password must be at least 6 characters long');
        return;
      }

      if (password !== confirmPassword) {
        showError('Passwords do not match');
        return;
      }

      setLoading(true);

      try {
        const response = await fetch('/api/auth/setup', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            username,
            email: email || undefined,
            password,
          }),
        });

        if (response.ok) {
          const data = await response.json();
          showSuccess('Account created successfully! Redirecting...');
          setTimeout(() => {
            window.location.href = '/';
          }, 1500);
        } else {
          const errorText = await response.text();
          showError(errorText || 'Failed to create account');
        }
      } catch (error) {
        showError('Network error. Please try again.');
      } finally {
        setLoading(false);
      }
    });

    // Check setup status on page load
    checkSetupStatus();
  </script>
</body>
</html>
